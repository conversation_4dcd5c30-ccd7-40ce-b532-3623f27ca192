import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/toast/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useSocket } from "@/hooks/use-socket";
import { client, trpc } from "@/lib/trpc/client";
import { useQueries } from "@tanstack/react-query";
import { SocketConnectionStatusIndicator } from "@/components/ui/SocketConnectionStatus";
import { MessageInterface } from "@/types/chat";

import { ApplicationStatus } from "@repo/server/src/types/campaign";
import { MessageType } from "@repo/server/src/types/chat";
import { User } from "@repo/server/src/models/user";
import { InformationCircleIcon } from "@heroicons/react/24/solid";

import MessageList from "./MessageList";

// TypeScript interfaces for proper type safety
interface ChatRoomProps {
  chatId: string;
  otherParticipant?: {
    id: string;
    name: string;
    userType: string;
    profilePicture?: {
      url: string | null;
      key: string | null;
      uploadedAt: string | null;
    } | null;
    brand?: {
      id: string;
      name: string;
    } | null;
  };
}

interface CampaignApplicationInterface {
  id: string;
  campaignId: string;
  athleteId: {
    _id: string;
    userId?: {
      _id: string;
      name: string;
    };
    profilePicture?: {
      url: string;
    };
  } | string;
  status: ApplicationStatus;
  appliedAt: string;
  updatedAt: string;
  message?: string;
  compensation?: number;
  deliverables?: string[];
}

interface SocketNewMessageEventInterface {
  id: string;
  chatId: string;
  content: string;
  type: MessageType;
  campaignId?: string;
  sender: {
    id: string;
    name: string;
    userType: string;
    profilePicture: {
      url: string | null;
      key: string | null;
      uploadedAt: string | null;
    } | null;
    brand: {
      id: string;
      name: string;
    } | null;
  };
  readBy: string[];
  createdAt: string;
  updatedAt: string;
}

interface SocketTypingEventInterface {
  chatId: string;
  userId: string;
  isTyping: boolean;
}

export function ChatRoom({ chatId, otherParticipant }: ChatRoomProps) {
  const { user } = useAuth();
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const utils = trpc.useUtils();
  const {
    socket,
    connectionState,
    joinChat,
    leaveChat,
    sendTypingStatus,
    markAsRead,
    forceReconnect,
    clearError,
  } = useSocket();
  const { toast } = useToast();
  const [updatingAppId, setUpdatingAppId] = useState<string | null>(null);
  const [respondedInvites, setRespondedInvites] = useState<Map<string, ApplicationStatus>>(new Map());

  const { data: messages, isLoading } = trpc.chat.getMessages.useQuery({
    chatId,
    limit: 50,
  }, {
    // Reduce aggressive refetching since we get real-time updates via socket
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  // Calculate unread messages
  const unreadMessages = messages?.filter(msg =>
    msg.sender?.id !== user?.id &&
    !msg.readBy.includes(user?.id || "")
  ) || [];

  // Update unread count when messages change
  useEffect(() => {
    setUnreadCount(unreadMessages.length);
  }, [unreadMessages.length]);



  const sendMessageMutation = trpc.chat.sendMessage.useMutation({
    onMutate: async (variables) => {
      // Cancel any outgoing refetches
      await utils.chat.getMessages.cancel({ chatId, limit: 50 });

      // Snapshot the previous value
      const previousMessages = utils.chat.getMessages.getData({ chatId, limit: 50 });

      // Create optimistic message
      const optimisticMessage = {
        id: `temp-${Date.now()}`,
        chatId: variables.chatId,
        content: variables.content,
        type: variables.type,
        campaignId: undefined,
        sender: {
          id: user?.id || "",
          name: user?.name || "",
          userType: (user?.userType as "athlete" | "brand") || "athlete",
          profilePicture: user?.athlete?.profilePicture || user?.brand?.logo || null,
          brand: user?.userType === "brand" ? {
            id: user?.brand?._id || "",
            name: user?.brand?.companyName || "",
          } : null,
        },
        readBy: [user?.id || ""],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isOptimistic: true, // Flag to identify optimistic messages
      };

      // Optimistically update the cache
      utils.chat.getMessages.setData({ chatId, limit: 50 }, (old) => {
        if (!old) return [optimisticMessage];
        return [...old, optimisticMessage];
      });

      setIsSending(true);

      // Return a context object with the snapshotted value
      return { previousMessages };
    },
    onError: (_err, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      utils.chat.getMessages.setData({ chatId, limit: 50 }, context?.previousMessages);

      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    },
    onSuccess: () => {
      setMessage("");
      utils.chat.getChats.invalidate();
      // Don't invalidate messages here as the real message will come through socket
    },
    onSettled: () => {
      setIsSending(false);
    },
  });

  // Helper to get application by campaignId and athleteId (only for brand users)
  const getApplicationId = async (
    campaignId: string,
    athleteId: string,
    senderAthleteProfileId?: string,
  ) => {
    // Only brand users can access campaign applications
    if (user?.userType !== "brand") {
      return null;
    }

    try {
      const apps = await client.campaign.getCampaignApplications.query({
        campaignId,
      });
      const app = apps.find((a) => {
        if (typeof a.athleteId === "object") {
          return (
            a.athleteId._id === athleteId ||
            a.athleteId.userId?._id === athleteId ||
            (senderAthleteProfileId &&
              a.athleteId._id === senderAthleteProfileId)
          );
        }
        return (
          a.athleteId === athleteId ||
          (senderAthleteProfileId && a.athleteId === senderAthleteProfileId)
        );
      });
      return app?.id;
    } catch (e) {
      console.error("Failed to fetch applications:", e);
      return null;
    }
  };

  const updateApplicationStatusMutation =
    trpc.campaign.updateApplicationStatus.useMutation();
  const respondToInviteMutation = trpc.campaign.respondToInvite.useMutation();

  // Join chat room when component mounts
  useEffect(() => {
    joinChat(chatId);
    // Mark as read after a short delay to allow messages to load
    setTimeout(() => markAsRead(chatId), 500);
    return () => leaveChat(chatId);
  }, [chatId, joinChat, leaveChat, markAsRead]);

  // Mark messages as read when user focuses on the window/tab
  useEffect(() => {
    const handleFocus = () => {
      markAsRead(chatId);
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [chatId, markAsRead]);

  // Listen for new messages
  useEffect(() => {
    if (!socket) return;

    const handleNewMessage = (newMessage: SocketNewMessageEventInterface) => {
      utils.chat.getMessages.setData({ chatId, limit: 50 }, (old) => {
        if (!old) return [newMessage];

        // Check if message already exists to avoid duplicates
        const messageExists = old.some((msg) => msg.id === newMessage.id);
        if (messageExists) return old;

        // If this is a message from the current user, remove any optimistic messages
        // that might match this content (to replace optimistic with real message)
        let filteredMessages = old;
        if (newMessage.sender.id === user?.id) {
          filteredMessages = old.filter((msg) =>
            !(msg as MessageInterface).isOptimistic ||
            msg.content !== newMessage.content ||
            Math.abs(new Date(msg.createdAt).getTime() - new Date(newMessage.createdAt).getTime()) > 5000
          );
        }

        // Add new message to the end (chronological order)
        return [...filteredMessages, newMessage];
      });

      // Only invalidate if the optimistic update failed (rare case)
      // This prevents unnecessary refetching since we get real-time updates

      utils.chat.getChats.invalidate();
      // Only mark as read if the message is from someone else and we're actively viewing the chat
      if (newMessage.sender.id !== user?.id) {
        markAsRead(chatId);
      }
    };

    const handleTyping = (data: SocketTypingEventInterface) => {
      if (data.userId !== user?.id) {
        setIsTyping(data.isTyping);
      }
    };

    const handleMessagesRead = () => {
      utils.chat.getMessages.invalidate({ chatId });
    };

    socket.on("newMessage", handleNewMessage);
    socket.on("userTyping", handleTyping);
    socket.on("messagesRead", handleMessagesRead);

    return () => {
      socket.off("newMessage", handleNewMessage);
      socket.off("userTyping", handleTyping);
      socket.off("messagesRead", handleMessagesRead);
    };
  }, [socket, chatId, user?.id, utils, markAsRead]);

  // Scroll to bottom when messages first load or when user sends a message
  useEffect(() => {
    if (messages && !hasScrolledToBottom) {
      // Scroll immediately on first load
      messagesEndRef.current?.scrollIntoView({ behavior: "auto" });
      setHasScrolledToBottom(true);
    }
  }, [messages, hasScrolledToBottom]);

  // Auto-scroll for new messages only if user is near bottom
  useEffect(() => {
    if (hasScrolledToBottom && messages) {
      const container = messagesEndRef.current?.parentElement;
      if (container) {
        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;
        if (isNearBottom) {
          messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
        }
      }
    }
  }, [messages, hasScrolledToBottom]);

  // Clean up stale optimistic messages after 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      utils.chat.getMessages.setData({ chatId, limit: 50 }, (old) => {
        if (!old) return old;

        const now = Date.now();
        return old.filter((msg) => {
          if (!(msg as MessageInterface).isOptimistic) return true;

          // Remove optimistic messages older than 30 seconds
          const messageAge = now - new Date(msg.createdAt).getTime();
          return messageAge < 30000;
        });
      });
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [chatId, utils]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || isSending) return;

    // Check connection status before sending
    if (!connectionState.isConnected) {
      toast({
        title: "Connection Error",
        description: "Unable to send message. Please check your connection and try again.",
        variant: "destructive",
      });
      return;
    }

    sendMessageMutation.mutate({
      chatId,
      content: message.trim(),
      type: MessageType.TEXT,
    });
    sendTypingStatus(chatId, false);
  };

  const handleTyping = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    sendTypingStatus(chatId, e.target.value.length > 0);
  };

  // Get the last message from each sender
  const lastMessageBySender = new Map<string, MessageInterface>();
  messages?.forEach((msg) => {
    if (msg.sender?.id) {
      lastMessageBySender.set(msg.sender.id, msg as MessageInterface);
    }
  });

  // Now it's safe to extract campaignIds and call useQueries
  const campaignIds = Array.from(
    new Set(
      messages
        ?.map((msg) => msg.campaignId)
        .filter((id): id is string => typeof id === "string" && !!id),
    ),
  );

  const applicationQueries = useQueries({
    queries: campaignIds.map((campaignId) => ({
      queryKey: [["campaignApplications", { campaignId }]],
      queryFn: () =>
        client.campaign.getCampaignApplications.query({ campaignId }),
      enabled: !!campaignId && user?.userType === "brand", // Only enable for brand users
    })),
  });

  // Build a lookup map for applications by campaignId
  const applicationsByCampaignId: Record<string, CampaignApplicationInterface[]> = {};
  applicationQueries.forEach((result, idx) => {
    if (result.data) {
      applicationsByCampaignId[campaignIds[idx]] = result.data as CampaignApplicationInterface[];
    }
  });

  // Helper to get application status for a message
  const getAppStatus = (msg: MessageInterface): ApplicationStatus | undefined => {
    if (
      typeof msg.campaignId === "string" &&
      typeof msg.sender?.id === "string" &&
      msg.campaignId in applicationsByCampaignId
    ) {
      const apps = applicationsByCampaignId[msg.campaignId];
      const found = apps?.find((a) => {
        if (typeof a.athleteId === "object") {
          return a.athleteId.userId?._id === msg.sender?.id;
        }
      });
      return found?.status;
    }
    return undefined;
  };

  // Helper to get invite status for a message (for athletes receiving invites)
  const getInviteStatus = (msg: MessageInterface): ApplicationStatus | undefined => {
    // First check if we've locally tracked this invite as responded to
    const inviteKey = `${msg.campaignId}:${msg.id}`;
    if (respondedInvites.has(inviteKey)) {
      return respondedInvites.get(inviteKey);
    }

    // Then check the backend data
    if (
      user?.userType === "athlete" &&
      typeof msg.campaignId === "string" &&
      msg.campaignId in applicationsByCampaignId
    ) {
      const apps = applicationsByCampaignId[msg.campaignId];
      const found = apps?.find((a) => {
        if (typeof a.athleteId === "object") {
          return a.athleteId.userId?._id === user?.id;
        }
        return false;
      });
      return found?.status;
    }
    return undefined;
  };

  // Accept/Reject application handlers
  const handleAcceptApplication = async (msg: MessageInterface) => {
    if (!msg.sender?.id || !msg.campaignId) return;

    setUpdatingAppId(msg.campaignId + ":" + msg.sender.id);
    const applicationId = await getApplicationId(
      msg.campaignId,
      msg.sender.id,
    );
    if (!applicationId) {
      toast({
        title: "Error",
        description: "Application not found",
        variant: "destructive",
      });
      setUpdatingAppId(null);
      return;
    }
    try {
      await updateApplicationStatusMutation.mutateAsync({
        applicationId,
        status: ApplicationStatus.ACCEPTED,
      });
      toast({
        title: "Success",
        description: "Application accepted",
        variant: "success",
      });
      utils.chat.getMessages.invalidate({ chatId });
      // Only invalidate campaign applications for brand users
      if (user?.userType === "brand") {
        utils.campaign.getCampaignApplications.invalidate({
          campaignId: msg.campaignId,
        });
      }
    } catch (e) {
      toast({
        title: "Error",
        description: e instanceof Error ? e.message : "Failed to accept application",
        variant: "destructive",
      });
    } finally {
      setUpdatingAppId(null);
    }
  };

  const handleRejectApplication = async (msg: MessageInterface) => {
    if (!msg.sender?.id || !msg.campaignId) return;

    setUpdatingAppId(msg.campaignId + ":" + msg.sender.id);
    const applicationId = await getApplicationId(
      msg.campaignId,
      msg.sender.id,
    );
    if (!applicationId) {
      toast({
        title: "Error",
        description: "Application not found",
        variant: "destructive",
      });
      setUpdatingAppId(null);
      return;
    }
    try {
      await updateApplicationStatusMutation.mutateAsync({
        applicationId,
        status: ApplicationStatus.REJECTED,
      });
      toast({
        title: "Success",
        description: "Application rejected",
        variant: "success",
      });
      utils.chat.getMessages.invalidate({ chatId });
      // Only invalidate campaign applications for brand users
      if (user?.userType === "brand") {
        utils.campaign.getCampaignApplications.invalidate({
          campaignId: msg.campaignId,
        });
      }
    } catch (e) {
      toast({
        title: "Error",
        description: e instanceof Error ? e.message : "Failed to reject application",
        variant: "destructive",
      });
    } finally {
      setUpdatingAppId(null);
    }
  };

  // Accept/Reject invite handlers for athletes
  const handleAcceptInvite = async (msg: MessageInterface) => {
    if (!msg.campaignId) {
      toast({
        title: "Error",
        description: "Campaign information not found",
        variant: "destructive",
      });
      return;
    }

    setUpdatingAppId(msg.campaignId + ":" + msg.id);
    try {
      // First respond to the invite on the backend
      await respondToInviteMutation.mutateAsync({
        campaignId: msg.campaignId,
        accept: true,
      });

      // Track that this invite has been responded to
      const inviteKey = `${msg.campaignId}:${msg.id}`;
      setRespondedInvites(prev => new Map(prev).set(inviteKey, ApplicationStatus.ACCEPTED));

      // Then send acceptance message
      await sendMessageMutation.mutateAsync({
        chatId,
        content: "I accept this campaign invitation!",
        type: MessageType.CAMPAIGN_ACCEPT,
        campaignId: msg.campaignId,
      });

      toast({
        title: "Success",
        description: "Campaign invitation accepted!",
        variant: "success",
      });
      utils.chat.getMessages.invalidate({ chatId });
      utils.campaign.getCampaignsForAthlete.invalidate();
      // Only invalidate campaign applications for brand users
      if (user?.userType === "brand") {
        utils.campaign.getCampaignApplications.invalidate({
          campaignId: msg.campaignId,
        });
      }
    } catch (e) {
      toast({
        title: "Error",
        description: e instanceof Error ? e.message : "Failed to accept invitation",
        variant: "destructive",
      });
    } finally {
      setUpdatingAppId(null);
    }
  };

  const handleRejectInvite = async (msg: MessageInterface) => {
    if (!msg.campaignId) {
      toast({
        title: "Error",
        description: "Campaign information not found",
        variant: "destructive",
      });
      return;
    }

    setUpdatingAppId(msg.campaignId + ":" + msg.id);
    try {
      // First respond to the invite on the backend
      await respondToInviteMutation.mutateAsync({
        campaignId: msg.campaignId,
        accept: false,
      });

      // Track that this invite has been responded to
      const inviteKey = `${msg.campaignId}:${msg.id}`;
      setRespondedInvites(prev => new Map(prev).set(inviteKey, ApplicationStatus.REJECTED));

      // Then send rejection message
      await sendMessageMutation.mutateAsync({
        chatId,
        content: "I decline this campaign invitation.",
        type: MessageType.CAMPAIGN_REJECT,
        campaignId: msg.campaignId,
      });

      toast({
        title: "Success",
        description: "Campaign invitation declined",
        variant: "success",
      });
      utils.chat.getMessages.invalidate({ chatId });
      // Only invalidate campaign applications for brand users
      if (user?.userType === "brand") {
        utils.campaign.getCampaignApplications.invalidate({
          campaignId: msg.campaignId,
        });
      }
    } catch (e) {
      toast({
        title: "Error",
        description: e instanceof Error ? e.message : "Failed to decline invitation",
        variant: "destructive",
      });
    } finally {
      setUpdatingAppId(null);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Get the other participant from messages (the person we're chatting with)
  const otherParticipantFromMessages = messages?.find(msg =>
    msg.sender?.id && msg.sender.id !== user?.id
  )?.sender;

  // Check if this is a self-chat by looking at the messages
  const isSelfChat = messages && messages.length > 0 &&
    messages.every(msg => msg.sender?.id === user?.id);

  // Use the prop if provided, otherwise fall back to the participant from messages
  const displayedParticipant = otherParticipant || otherParticipantFromMessages;

  return (
    <div className="tw-flex tw-flex-col tw-overflow-hidden tw-mx-3">
      {/* Connection Status */}
      <div className="tw-px-4 tw-py-1">
        <SocketConnectionStatusIndicator
          connectionState={connectionState}
          onReconnect={forceReconnect}
          onClearError={clearError}
          className="tw-mb-0"
        />
      </div>

      {/* Chat Header - Who you're chatting with */}
      {isSelfChat ? (
        <div className="tw-px-4 tw-py-2 tw-flex tw-items-center tw-justify-center">
          <div className="tw-flex tw-items-center tw-gap-3">
            <div className="tw-relative tw-w-16 tw-h-16 tw-mr-2">
              <InformationCircleIcon className="tw-w-16 tw-h-16 tw-text-aims-text-primary" />
            </div>
            <div>
              <h3 className="tw-font-semibold tw-text-aims-text-primary tw-text-2xl">
                System Notifications
              </h3>
              <p className="tw-text-sm tw-text-aims-text-secondary">
                Earnings, payouts, and system updates
              </p>
            </div>
          </div>
        </div>
      ) : displayedParticipant && (
        <div className="tw-px-4 tw-py-2 tw-flex tw-items-center tw-justify-center">
          <Link
            href={displayedParticipant.userType === "brand"
              ? `/app/brand/${displayedParticipant.brand?.id}`
              : `/app/athlete/${displayedParticipant.id}`
            }
            className="tw-flex tw-items-center tw-gap-3"
          >
            <Image
              src={displayedParticipant.profilePicture?.url || "/no-profile-pic.jpg"}
              alt={displayedParticipant.name}
              className="tw-w-10 tw-h-10 tw-rounded-full tw-object-cover"
              width={150}
              height={150}
            />
            <div>
              <h3 className="tw-font-semibold tw-text-aims-text-primary tw-text-2xl">
                {displayedParticipant.name}
              </h3>
              {displayedParticipant.brand?.name && (
                <p className="tw-text-sm tw-text-aims-text-secondary">
                  {displayedParticipant.brand.name}
                </p>
              )}
            </div>
          </Link>
        </div>
      )}

      {/* Messages */}
      {isLoading ? (
        <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-h-[calc(100vh-200px)] tw-space-y-4">
          <div className="tw-animate-spin tw-rounded-full tw-h-8 tw-w-8 tw-border-b-2 tw-border-aims-primary"></div>
          <p className="tw-text-aims-text-secondary">Loading messages...</p>
          {unreadCount > 0 && (
            <div className="tw-bg-aims-primary tw-text-white tw-px-3 tw-py-1 tw-rounded-full tw-text-sm">
              {unreadCount} new message{unreadCount !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      ) : (
        <>
          <div className="tw-relative tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden tw-p-4 tw-space-y-4 tw-rounded-lg tw-border tw-border-aims-dark-4 tw-max-h-[calc(100vh-275px)] tw-min-h-[calc(100vh-275px)] tw-w-full tw-max-w-full">


            <MessageList
              messages={(messages || []) as MessageInterface[]}
              user={user as User}
              lastMessageBySender={lastMessageBySender}
              isTyping={isTyping}
              messagesEndRef={messagesEndRef}
              updatingAppId={updatingAppId}
              onAcceptApplication={handleAcceptApplication}
              onRejectApplication={handleRejectApplication}
              onAcceptInvite={handleAcceptInvite}
              onRejectInvite={handleRejectInvite}
              getAppStatus={getAppStatus}
              getInviteStatus={getInviteStatus}
              unreadMessages={unreadMessages as MessageInterface[]}
            />

            {/* Scroll to bottom button for unread messages */}
            {unreadCount > 0 && hasScrolledToBottom && (
              <button
                onClick={scrollToBottom}
                className="tw-fixed tw-bottom-24 tw-right-8 tw-bg-aims-primary tw-text-white tw-rounded-full tw-p-3 tw-shadow-lg tw-z-20 tw-flex tw-items-center tw-gap-2 hover:tw-bg-aims-primary/90 tw-transition-colors"
              >
                <svg className="tw-w-4 tw-h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
                <span className="tw-text-sm">{unreadCount} new</span>
              </button>
            )}
          </div>
          {/* Message input - Hide for self-chats */}
          {!isSelfChat && (
            <form onSubmit={handleSendMessage} className="tw-p-4">
              <div className="tw-flex tw-gap-2 tw-items-end">
                <Textarea
                  value={message}
                  onChange={handleTyping}
                  placeholder="Type a message..."
                  className="tw-flex-1 tw-bg-aims-dark-2 tw-min-h-[40px] tw-max-h-[120px] tw-resize-none tw-overflow-y-auto tw-whitespace-pre-wrap tw-break-words"
                  rows={1}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage(e);
                    }
                  }}
                  style={{
                    height: 'auto',
                    minHeight: '40px',
                    maxHeight: '120px'
                  }}
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    target.style.height = 'auto';
                    target.style.height = Math.min(target.scrollHeight, 120) + 'px';
                  }}
                />
                <Button
                  type="submit"
                  disabled={!message.trim() || isSending || !connectionState.isConnected}
                  className="tw-self-end"
                >
                  {isSending ? "Sending..." : connectionState.isConnected ? "Send" : "Disconnected"}
                </Button>
              </div>
            </form>
          )}

          {/* Self-chat info message */}
          {isSelfChat && (
            <div className="tw-p-4 tw-bg-aims-dark-2 tw-rounded">
              <p className="tw-text-sm tw-text-aims-text-secondary tw-text-center">
                This is your system notifications chat. You&apos;ll receive updates about earnings, payouts, and other important information here.
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );
}
